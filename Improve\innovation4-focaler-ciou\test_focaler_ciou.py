"""
Focaler-CIoU 损失函数测试脚本

测试Focaler-CIoU损失函数的正确性和性能
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from focaler_ciou_loss import (
    compute_iou, 
    compute_ciou, 
    compute_focaler_iou, 
    compute_focaler_ciou_loss,
    FocalerCIoUBboxLoss
)

def test_basic_functions():
    """测试基础函数的正确性"""
    print("🧪 测试基础函数...")
    
    # 创建测试数据
    pred_boxes = torch.tensor([[10, 10, 50, 50], [20, 20, 60, 60]], dtype=torch.float32)
    target_boxes = torch.tensor([[15, 15, 55, 55], [25, 25, 65, 65]], dtype=torch.float32)
    
    # 测试IoU计算
    iou = compute_iou(pred_boxes, target_boxes, xywh=False)
    print(f"✅ IoU计算: {iou}")
    
    # 测试CIoU计算
    ciou = compute_ciou(pred_boxes, target_boxes, xywh=False)
    print(f"✅ CIoU计算: {ciou}")
    
    # 测试Focaler-IoU变换
    focaler_iou = compute_focaler_iou(iou, d=0.0, u=0.95)
    print(f"✅ Focaler-IoU变换: {focaler_iou}")
    
    # 测试最终损失
    loss = compute_focaler_ciou_loss(pred_boxes, target_boxes, xywh=False)
    print(f"✅ Focaler-CIoU损失: {loss}")
    
    return True

def test_focaler_transformation():
    """测试Focaler变换的分段特性"""
    print("\n🔍 测试Focaler变换特性...")
    
    # 创建IoU值范围
    iou_values = torch.linspace(0, 1, 21)  # 0.0 到 1.0，步长0.05
    
    # 测试不同参数组合
    configs = [
        {'d': 0.0, 'u': 0.95, 'name': '默认参数'},
        {'d': 0.1, 'u': 0.9, 'name': '收紧范围'},
        {'d': 0.0, 'u': 0.8, 'name': '降低上阈值'},
    ]
    
    for config in configs:
        focaler_values = compute_focaler_iou(iou_values, d=config['d'], u=config['u'])
        print(f"\n📊 {config['name']} (d={config['d']}, u={config['u']}):")
        
        for i, (iou, focaler) in enumerate(zip(iou_values, focaler_values)):
            if i % 4 == 0:  # 每4个值打印一次
                print(f"   IoU={iou:.2f} -> Focaler-IoU={focaler:.3f}")
    
    return True

def visualize_focaler_transformation():
    """可视化Focaler变换"""
    print("\n📈 生成Focaler变换可视化...")
    
    # 创建IoU值范围
    iou_range = torch.linspace(0, 1, 1000)
    
    # 不同参数配置
    configs = [
        {'d': 0.0, 'u': 0.95, 'label': 'Default (d=0.0, u=0.95)', 'color': 'blue'},
        {'d': 0.1, 'u': 0.9, 'label': 'Tight (d=0.1, u=0.9)', 'color': 'red'},
        {'d': 0.0, 'u': 0.8, 'label': 'Low-u (d=0.0, u=0.8)', 'color': 'green'},
    ]
    
    plt.figure(figsize=(10, 6))
    
    for config in configs:
        focaler_values = compute_focaler_iou(iou_range, d=config['d'], u=config['u'])
        plt.plot(iou_range.numpy(), focaler_values.numpy(), 
                label=config['label'], color=config['color'], linewidth=2)
    
    # 添加对角线参考
    plt.plot([0, 1], [0, 1], '--', color='gray', alpha=0.5, label='y=x (Identity)')
    
    plt.xlabel('Original IoU')
    plt.ylabel('Focaler IoU')
    plt.title('Focaler-IoU Transformation Curves')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.xlim(0, 1)
    plt.ylim(0, 1)
    
    # 保存图片
    plt.savefig('focaler_transformation.png', dpi=300, bbox_inches='tight')
    print("✅ 可视化图片已保存为 'focaler_transformation.png'")
    plt.close()
    
    return True

def test_loss_class():
    """测试损失函数类"""
    print("\n🏗️ 测试FocalerCIoUBboxLoss类...")
    
    try:
        # 创建损失函数实例
        loss_fn = FocalerCIoUBboxLoss(reg_max=16, d=0.0, u=0.95)
        print("✅ 损失函数类创建成功")
        
        # 创建模拟数据
        batch_size = 2
        num_anchors = 100
        
        pred_dist = torch.randn(batch_size, num_anchors, 16)  # DFL分布
        pred_bboxes = torch.rand(batch_size, num_anchors, 4) * 100  # 预测框
        anchor_points = torch.rand(batch_size, num_anchors, 2) * 100  # 锚点
        target_bboxes = torch.rand(batch_size, num_anchors, 4) * 100  # 目标框
        target_scores = torch.rand(batch_size, num_anchors, 2)  # 目标分数
        target_scores_sum = target_scores.sum()
        fg_mask = torch.rand(batch_size, num_anchors) > 0.5  # 前景掩码
        
        # 前向传播测试
        loss_iou, loss_dfl = loss_fn(
            pred_dist, pred_bboxes, anchor_points, 
            target_bboxes, target_scores, target_scores_sum, fg_mask
        )
        
        print(f"✅ 前向传播成功:")
        print(f"   IoU损失: {loss_iou:.6f}")
        print(f"   DFL损失: {loss_dfl:.6f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 损失函数类测试失败: {e}")
        return False

def performance_comparison():
    """性能对比测试"""
    print("\n⚡ 性能对比测试...")
    
    # 创建大量测试数据
    num_boxes = 10000
    pred_boxes = torch.rand(num_boxes, 4) * 100
    target_boxes = torch.rand(num_boxes, 4) * 100
    
    # 确保boxes格式正确 (x1, y1, x2, y2)
    pred_boxes[:, 2:] += pred_boxes[:, :2]  # x2 = x1 + w, y2 = y1 + h
    target_boxes[:, 2:] += target_boxes[:, :2]
    
    import time
    
    # 测试标准IoU计算时间
    start_time = time.time()
    for _ in range(100):
        iou = compute_iou(pred_boxes, target_boxes, xywh=False)
    iou_time = time.time() - start_time
    
    # 测试CIoU计算时间
    start_time = time.time()
    for _ in range(100):
        ciou = compute_ciou(pred_boxes, target_boxes, xywh=False)
    ciou_time = time.time() - start_time
    
    # 测试Focaler-CIoU计算时间
    start_time = time.time()
    for _ in range(100):
        focaler_loss = compute_focaler_ciou_loss(pred_boxes, target_boxes, xywh=False)
    focaler_time = time.time() - start_time
    
    print(f"📊 性能对比结果 ({num_boxes}个框，100次迭代):")
    print(f"   IoU计算时间: {iou_time:.4f}s")
    print(f"   CIoU计算时间: {ciou_time:.4f}s")
    print(f"   Focaler-CIoU计算时间: {focaler_time:.4f}s")
    print(f"   相对开销: {focaler_time/iou_time:.2f}x IoU时间")
    
    return True

def test_edge_cases():
    """测试边界情况"""
    print("\n🔍 测试边界情况...")
    
    # 完全重叠的框
    identical_boxes = torch.tensor([[10, 10, 50, 50], [10, 10, 50, 50]], dtype=torch.float32)
    loss_identical = compute_focaler_ciou_loss(identical_boxes, identical_boxes, xywh=False)
    print(f"✅ 完全重叠框损失: {loss_identical} (应该接近0)")
    
    # 完全不重叠的框
    no_overlap_pred = torch.tensor([[0, 0, 10, 10]], dtype=torch.float32)
    no_overlap_target = torch.tensor([[50, 50, 60, 60]], dtype=torch.float32)
    loss_no_overlap = compute_focaler_ciou_loss(no_overlap_pred, no_overlap_target, xywh=False)
    print(f"✅ 完全不重叠框损失: {loss_no_overlap} (应该较大)")
    
    # 零尺寸框处理
    try:
        zero_size_pred = torch.tensor([[10, 10, 10, 10]], dtype=torch.float32)
        normal_target = torch.tensor([[10, 10, 20, 20]], dtype=torch.float32)
        loss_zero = compute_focaler_ciou_loss(zero_size_pred, normal_target, xywh=False)
        print(f"✅ 零尺寸框处理: {loss_zero}")
    except Exception as e:
        print(f"⚠️ 零尺寸框处理异常: {e}")
    
    return True

def main():
    """主测试函数"""
    print("🚀 Focaler-CIoU 损失函数测试开始")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        test_basic_functions,
        test_focaler_transformation,
        visualize_focaler_transformation,
        test_loss_class,
        performance_comparison,
        test_edge_cases,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ {test.__name__} 测试失败")
        except Exception as e:
            print(f"❌ {test.__name__} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("✅ 所有测试通过！Focaler-CIoU实现正确。")
    else:
        print("⚠️ 部分测试失败，请检查实现。")

if __name__ == "__main__":
    main()
