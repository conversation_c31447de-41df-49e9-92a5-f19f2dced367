#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 火焰烟雾检测最佳配置训练脚本
基于超参数调优结果的最优配置

最佳配置来源: 手动网格搜索结果
- lr0: 0.0006
- cls: 0.5  
- box: 8.0
- batch: 24
- mAP50: 0.67542 (10轮训练结果)

目标: 使用最佳配置进行完整训练，冲刺更高性能
"""

import os
import sys
import warnings
import torch
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from ultralytics import YOLO
from ultralytics.utils import LOGGER

# 忽略警告
warnings.filterwarnings('ignore')

def check_environment():
    """检查环境配置"""
    print("🔧 检查环境配置...")
    
    # 检查CUDA
    if torch.cuda.is_available():
        print(f"✅ GPU: {torch.cuda.get_device_name(0)}")
        print(f"📊 显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
    else:
        print("⚠️ 使用CPU训练")
    
    # 检查数据集
    dataset_path = "ultralytics/cfg/datasets/fire-smoke-dataset.yaml"
    if os.path.exists(dataset_path):
        print(f"✅ 数据集: {dataset_path}")
    else:
        raise FileNotFoundError(f"❌ 数据集不存在: {dataset_path}")

def get_best_config():
    """获取调优后的最佳配置"""
    return {
        # === 核心超参数 (来自调优结果) ===
        "lr0": 0.0006,              # 最佳初始学习率
        "cls": 0.5,                 # 最佳分类损失权重
        "box": 8.0,                 # 最佳边界框损失权重
        "batch": 24,                # 最佳批次大小
        
        # === 训练设置 ===
        "epochs": 100,               # 增加训练轮数
        "imgsz": 640,               # 标准图像尺寸
        "device": "0" if torch.cuda.is_available() else "cpu",
        "workers": 8,               # 优化的工作线程数
        
        # === 数据集 ===
        "data": "ultralytics/cfg/datasets/fire-smoke-dataset.yaml",
        
        # === 项目设置 ===
        "project": "Improve/innovation1-c2f-emscp-aaaf/tune_results",
        "name": f"best_config_training_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "exist_ok": True,
        
        # === 优化器设置 ===
        "optimizer": "AdamW",
        "lrf": 0.0001,              # 最终学习率因子
        "momentum": 0.937,
        "weight_decay": 0.0008,
        "cos_lr": True,             # 余弦学习率调度
        "warmup_epochs": 3.0,
        "warmup_momentum": 0.8,
        "warmup_bias_lr": 0.1,
        
        # === 损失权重 ===
        "dfl": 1.5,                 # 分布焦点损失权重
        
        # === 数据增强 (火焰烟雾优化) ===
        "hsv_h": 0.01,              # 色调增强 (保护火焰橙红色)
        "hsv_s": 0.6,               # 饱和度增强
        "hsv_v": 0.3,               # 明度增强
        "degrees": 8.0,             # 旋转角度
        "translate": 0.08,          # 平移
        "scale": 0.5,               # 缩放
        "shear": 0.0,               # 剪切
        "perspective": 0.0,         # 透视变换
        "flipud": 0.0,              # 垂直翻转
        "fliplr": 0.4,              # 水平翻转
        "mosaic": 0.5,              # 马赛克增强
        "mixup": 0.0,               # 混合增强
        "copy_paste": 0.0,          # 复制粘贴增强
        "close_mosaic": 15,         # 关闭马赛克的轮数
        
        # === 训练优化 ===
        "amp": True,                # 自动混合精度
        "cache": False,             # 不缓存图像
        "multi_scale": False,       # 不使用多尺度训练
        "patience": 15,             # 早停耐心值
        "save_period": 10,          # 保存周期
        
        # === 验证设置 ===
        "val": True,
        "plots": True,              # 生成训练图表
        "verbose": True,
    }

def train_with_best_config():
    """使用最佳配置进行训练"""
    print("🚀 开始使用最佳配置训练...")
    
    # 获取最佳配置
    config = get_best_config()
    
    print("🎯 最佳配置参数:")
    print(f"  学习率: {config['lr0']}")
    print(f"  分类权重: {config['cls']}")
    print(f"  边界框权重: {config['box']}")
    print(f"  批次大小: {config['batch']}")
    print(f"  训练轮数: {config['epochs']}")
    print(f"  图像尺寸: {config['imgsz']}")
    print()
    
    # 创建模型
    model = YOLO("yolov8n.pt")
    
    # 开始训练
    print("🔥 开始训练...")
    results = model.train(**config)
    
    print("✅ 训练完成!")
    return results

def analyze_results(results):
    """分析训练结果"""
    print("\n📊 训练结果分析:")
    print("=" * 50)
    
    try:
        if hasattr(results, 'results_dict'):
            metrics = results.results_dict
            
            # 获取关键指标
            mAP50 = metrics.get('metrics/mAP50(B)', 0)
            mAP50_95 = metrics.get('metrics/mAP50-95(B)', 0)
            precision = metrics.get('metrics/precision(B)', 0)
            recall = metrics.get('metrics/recall(B)', 0)
            
            print(f"📈 最终性能指标:")
            print(f"  mAP50: {mAP50:.5f}")
            print(f"  mAP50-95: {mAP50_95:.5f}")
            print(f"  Precision: {precision:.5f}")
            print(f"  Recall: {recall:.5f}")
            
            # 与基准对比
            baseline_mAP50 = 0.75445      # 基准模型
            bifpn_mAP50 = 0.76862         # BiFPN增强
            c2f_emscp_mAP50 = 0.78026     # C2f-EMSCP最佳
            tuning_best = 0.67542         # 调优最佳(10轮)
            
            print(f"\n🎯 性能对比:")
            print(f"  vs 基准模型 (0.75445): {(mAP50 - baseline_mAP50) * 100:+.2f}%")
            print(f"  vs BiFPN (0.76862): {(mAP50 - bifpn_mAP50) * 100:+.2f}%")
            print(f"  vs C2f-EMSCP (0.78026): {(mAP50 - c2f_emscp_mAP50) * 100:+.2f}%")
            print(f"  vs 调优结果 (0.67542): {(mAP50 - tuning_best) * 100:+.2f}%")
            
            # 性能评估
            if mAP50 > c2f_emscp_mAP50:
                print("🚀 创造新的最佳记录!")
            elif mAP50 > bifpn_mAP50:
                print("🎉 成功超越BiFPN!")
            elif mAP50 > baseline_mAP50:
                print("✅ 超越基准模型!")
            elif mAP50 > tuning_best:
                print("📈 相比调优结果有提升!")
            else:
                print("⚠️ 性能未达预期，建议进一步调优")
            
        else:
            print("⚠️ 无法获取详细结果")
            
    except Exception as e:
        print(f"❌ 结果分析出错: {e}")

def main():
    """主函数"""
    print("🎯 火焰烟雾检测最佳配置训练")
    print("基于超参数调优的最优配置")
    print("=" * 60)
    
    try:
        # 1. 环境检查
        check_environment()
        print()
        
        # 2. 确认训练
        print("📋 训练配置:")
        config = get_best_config()
        print(f"  模型: YOLOv8n")
        print(f"  训练轮数: {config['epochs']}")
        print(f"  批次大小: {config['batch']}")
        print(f"  学习率: {config['lr0']}")
        print(f"  预计时间: ~{config['epochs'] * 0.02:.1f}小时")
        
        confirm = input("\n是否开始训练? (y/n): ").strip().lower()
        if confirm != 'y':
            print("❌ 训练已取消")
            return
        
        # 3. 开始训练
        results = train_with_best_config()
        
        # 4. 分析结果
        analyze_results(results)
        
        print("\n" + "=" * 60)
        print("🎉 最佳配置训练完成!")
        print("📁 结果保存在: Improve/innovation1-c2f-emscp-aaaf/tune_results/")
        print("📋 下一步:")
        print("  1. 查看训练图表和指标")
        print("  2. 验证模型在测试集上的性能")
        print("  3. 部署模型进行实际应用测试")
        
    except KeyboardInterrupt:
        print("\n⚠️ 训练被用户中断")
    except Exception as e:
        print(f"\n❌ 训练出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
