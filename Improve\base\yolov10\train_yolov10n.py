import warnings, os
# os.environ["CUDA_VISIBLE_DEVICES"]="-1"    # 代表用cpu训练 不推荐！没意义！ 而且有些模块不能在cpu上跑
# os.environ["CUDA_VISIBLE_DEVICES"]="0"     # 代表用第一张卡进行训练  0：第一张卡 1：第二张卡
warnings.filterwarnings('ignore')
from ultralytics import YOLO

# YOLOv10n 对比算法训练脚本
# 使用最小版本的YOLOv10n模型作为基准对比算法
# 数据集配置参考主目录的train.py文件

if __name__ == '__main__':
    # 使用YOLOv10n最小版本模型
    model = YOLO('yolov10n.pt')  # 加载预训练的YOLOv10n模型
    
    # 开始训练 - 使用基础参数配置，不进行参数调优
    model.train(
        # 数据集配置 - 与主训练脚本保持一致
        data='ultralytics/cfg/datasets/fire-smoke-dataset.yaml',
        
        # 训练基本参数 - 使用默认配置
        epochs=100,              # 训练轮数
        patience=40,             # 早停耐心值
        batch=-1,                # 自动选择最优batch size
        imgsz=640,               # 输入图像尺寸
        
        # 设备设置
        device="0" if os.environ.get('CUDA_VISIBLE_DEVICES') != '-1' else "cpu",
        workers=8,               # 数据加载线程数
        
        # 项目和名称设置
        project='runs/train',
        name='fire-smoke-dataset-yolov10n-baseline',  # 基准模型标识
        exist_ok=True,
        
        # 模型设置
        pretrained=True,         # 使用预训练权重
        
        # 使用默认优化器设置，不进行调优
        optimizer="auto",        # 自动选择优化器
        lr0=0.01,               # 默认学习率
        lrf=0.01,               # 默认最终学习率因子
        momentum=0.937,          # 默认动量
        weight_decay=0.0005,     # 默认权重衰减
        
        # 学习率调度 - 默认设置
        cos_lr=False,           # 不使用余弦学习率调度
        warmup_epochs=3.0,      # 默认预热轮数
        warmup_momentum=0.8,    # 默认预热动量
        warmup_bias_lr=0.1,     # 默认预热偏置学习率
        
        # 其他训练参数 - 默认设置
        verbose=True,
        seed=42,                # 保持随机种子一致以确保可重复性
        deterministic=True,
        single_cls=False,       # 双类别检测
        rect=False,
        close_mosaic=10,        # 默认马赛克关闭时间
        resume=False,
        amp=True,               # 使用自动混合精度训练
        fraction=1.0,           # 使用100%数据
        profile=False,
        freeze=None,
        
        # 数据增强参数 - 使用默认设置
        hsv_h=0.015,            # 默认色调变化
        hsv_s=0.7,              # 默认饱和度变化
        hsv_v=0.4,              # 默认亮度变化
        degrees=0.0,            # 默认旋转
        translate=0.1,          # 默认平移
        scale=0.5,              # 默认缩放
        shear=0.0,              # 默认剪切
        perspective=0.0,        # 默认透视
        flipud=0.0,             # 默认垂直翻转
        fliplr=0.5,             # 默认水平翻转
        mosaic=1.0,             # 默认马赛克
        mixup=0.0,              # 默认mixup
        copy_paste=0.0,         # 默认复制粘贴
        
        # 损失权重 - 使用默认设置
        box=7.5,                # 默认框回归权重
        cls=0.5,                # 默认分类权重
        dfl=1.5,                # 默认分布焦点损失权重
        
        # 缓存设置
        cache=True,             # 使用缓存
    )
