# YOLO 基准对比算法

本目录包含用于对比实验的基准YOLO模型训练脚本。

## 目录结构

```
base/
├── yolov5/
│   └── train_yolov5n.py    # YOLOv5n基准训练脚本
├── yolov10/
│   └── train_yolov10n.py   # YOLOv10n基准训练脚本
├── yolov11/
│   └── train_yolov11n.py   # YOLOv11n基准训练脚本
└── README.md               # 本说明文件
```

## 模型说明

### YOLOv5n
- **模型文件**: `yolov5n.pt`
- **特点**: YOLOv5系列的最小版本，参数量最少，速度最快
- **用途**: 作为YOLOv5系列的基准对比模型

### YOLOv10n
- **模型文件**: `yolov10n.pt`
- **特点**: YOLOv10系列的最小版本，在YOLOv5基础上进行了优化
- **用途**: 作为YOLOv10系列的基准对比模型

### YOLOv11n
- **模型文件**: `yolo11n.pt`
- **特点**: YOLOv11系列的最小版本，最新的YOLO架构
- **用途**: 作为YOLOv11系列的基准对比模型

## 数据集配置

所有训练脚本都使用相同的数据集配置：
- **数据集**: `ultralytics/cfg/datasets/fire-smoke-dataset.yaml`
- **类别**: 双分类（火焰 + 烟雾）
- **图像尺寸**: 640x640

## 训练参数

所有基准模型使用**默认参数配置**，不进行参数调优：

### 基本参数
- **训练轮数**: 100 epochs
- **早停耐心**: 40 epochs
- **批次大小**: 自动选择
- **学习率**: 0.01（默认）
- **优化器**: auto（自动选择）

### 数据增强
使用默认的数据增强参数，不进行特殊优化

### 损失权重
- **框回归权重**: 7.5（默认）
- **分类权重**: 0.5（默认）
- **分布焦点损失权重**: 1.5（默认）

## 使用方法

### 1. 运行YOLOv5n基准训练
```bash
cd Improve/base/yolov5
python train_yolov5n.py
```

### 2. 运行YOLOv10n基准训练
```bash
cd Improve/base/yolov10
python train_yolov10n.py
```

### 3. 运行YOLOv11n基准训练
```bash
cd Improve/base/yolov11
python train_yolov11n.py
```

## 训练结果

训练结果将保存在 `runs/train/` 目录下：
- `fire-smoke-dataset-yolov5n-baseline/`
- `fire-smoke-dataset-yolov10n-baseline/`
- `fire-smoke-dataset-yolov11n-baseline/`

每个结果目录包含：
- `weights/best.pt` - 最佳权重文件
- `weights/last.pt` - 最后一轮权重文件
- `results.png` - 训练曲线图
- `confusion_matrix.png` - 混淆矩阵
- 其他训练日志和可视化文件

## 注意事项

1. **预训练权重**: 所有模型都使用预训练权重进行训练
2. **随机种子**: 设置为42，确保结果可重复
3. **设备配置**: 自动检测GPU，如无GPU则使用CPU
4. **参数调优**: 基准模型不进行参数调优，使用默认配置
5. **对比目的**: 这些基准结果用于与改进模型进行性能对比

## 性能对比

运行完所有基准模型后，可以通过以下指标进行对比：
- **mAP@0.5**: 在IoU=0.5时的平均精度
- **mAP@0.5:0.95**: 在IoU=0.5-0.95时的平均精度
- **Precision**: 精确率
- **Recall**: 召回率
- **参数量**: 模型参数数量
- **FLOPs**: 浮点运算次数
- **推理速度**: 每张图像的推理时间

这些基准结果将作为评估改进模型效果的重要参考。
