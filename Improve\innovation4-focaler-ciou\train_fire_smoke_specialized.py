"""
创新点4 (Focaler-CIoU) 火焰烟雾专用训练脚本

基于创新点1 (C2f-EMSCP-AAAF) 架构，集成Focaler-CIoU损失函数：
1. 火焰特征保护：保持橙红色特征，适度形状变换
2. 烟雾特征增强：增强灰度变化，模拟不同密度烟雾
3. 环境适应性：模拟不同光照、天气条件
4. Focaler-CIoU损失：改进的边界框回归损失函数
5. C2f-EMSCP-AAAF友好：充分利用多尺度和注意力特性
"""

import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 获取脚本所在目录并切换
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)

# 添加项目根目录到路径
sys.path.append('../../')

from ultralytics import YOLO
from ultralytics.models.yolo.detect import DetectionTrainer
from ultralytics.utils.loss import v8DetectionLoss
import torch
from focaler_ciou_loss import FocalerCIoUBboxLoss

def get_fire_smoke_specialized_augmentation():
    """
    火焰烟雾专用数据增强参数 - 基于0.78mAP成功配置v3

    设计原则：更温和的增强策略，基于成功实验配置
    1. 🔥 火焰特征：保护橙红色调，减少过度变化
    2. 💨 烟雾特征：适度灰度变化，避免过度增强
    3. 🌍 环境适应：温和的光照和几何变换
    4. 🧠 注意力友好：保持关键特征区域完整性
    """
    return {
        # === 火焰烟雾专用颜色增强（更温和）===
        'hsv_h': 0.01,       # 🔥 更小的色调变化（基于0.78mAP成功配置）
        'hsv_s': 0.6,        # 🔥 适度饱和度变化（基于成功配置）
        'hsv_v': 0.3,        # 💨 适度亮度变化（基于成功配置）

        # === 火焰动态特征模拟（更温和）===
        'degrees': 8.0,      # 🔥 更小的旋转角度（基于成功配置）
        'translate': 0.08,   # 🔥 更小的位置变化（基于成功配置）
        'scale': 0.5,        # 🔥 更温和的大小变化（基于成功配置）
        'shear': 0.0,        # 🔥 关闭剪切变换（基于成功配置）

        # === 环境条件模拟 ===
        'perspective': 0.0,  # 🏠 关闭透视变换（基于成功配置）
        'flipud': 0.0,       # 🔥 火焰向上特性，禁用垂直翻转
        'fliplr': 0.4,       # 🏠 建筑物对称性（基于成功配置）

        # === 复杂场景增强（更温和）===
        'mosaic': 0.5,       # 🏙️ 适度马赛克（基于成功配置）
        'mixup': 0.0,        # 🌫️ 关闭mixup（基于成功配置）
        'copy_paste': 0.0,   # 🔥 关闭copy_paste（基于成功配置）

        # === 遮挡和干扰模拟 ===
        'erasing': 0.4,      # 🌫️ 随机擦除（基于成功配置）
        'auto_augment': 'randaugment',  # 🎲 自动增强

        # === 高级增强策略 ===
        'bgr': 0.0,          # 🎨 保持RGB通道顺序
        'crop_fraction': 1.0, # 📐 完整图像，避免裁剪关键区域
    }

def get_fire_smoke_optimizer_config():
    """火焰烟雾检测优化的训练参数 - 基于0.78mAP成功配置v3"""
    return {
        # === 学习率策略 ===
        'lr0': 0.0006,       # 🎯 更低的初始学习率（基于0.78mAP成功实验）
        'lrf': 0.0001,       # 📉 更低的最终学习率（基于成功配置）
        'momentum': 0.937,   # 🚀 标准动量
        'weight_decay': 0.0008, # ⚖️ 适度正则化

        # === 预热策略 ===
        'warmup_epochs': 3.0,    # 🔥 预热期（基于成功配置）
        'warmup_momentum': 0.8,  # 🌡️ 预热动量
        'warmup_bias_lr': 0.1,   # 📊 预热偏置学习率

        # === 学习率调度 ===
        'cos_lr': True,      # 📈 余弦学习率调度
    }

def get_fire_smoke_loss_weights():
    """火焰烟雾检测专用损失权重 - 基于0.78mAP成功配置v3"""
    return {
        'box': 8.0,          # 📦 边界框权重（基于0.78mAP成功实验）
        'cls': 0.5,          # 🏷️ 分类权重（关键：降低到0.5，成功配置的核心）
        'dfl': 1.5,          # 📏 DFL权重（保持与成功实验一致）
    }

def get_focaler_ciou_config():
    """Focaler-CIoU损失函数配置参数 - 优化版本v2"""
    return {
        'd': 0.15,           # 🎯 下阈值：从0.00→0.15，忽略低质量样本
        'u': 0.90,           # 🎯 上阈值：从0.95→0.90，放宽高质量门槛
        'reg_max': 16,       # 📏 DFL最大回归值
    }

def get_fire_smoke_training_strategy():
    """火焰烟雾检测训练策略 - 基于0.78mAP成功配置v3"""
    return {
        'close_mosaic': 15,  # 🧩 较早关闭马赛克（基于0.78mAP成功配置）
        'patience': 15,      # 🎯 早停耐心值（基于成功配置）
        'save_period': 10,   # 💾 定期保存（基于成功配置）
        'val': True,         # ✅ 启用验证
        'plots': True,       # 📊 生成图表
        'cache': False,      # 💾 关闭缓存（基于成功配置）
        'amp': True,         # ⚡ 混合精度训练
        'single_cls': False, # 🏷️ 多类别检测
        'rect': False,       # 📐 不使用矩形训练
        'deterministic': True, # 🎲 确定性训练
        'seed': 42,          # 🌱 随机种子
    }

def main():
    try:
        print("🔥 创新点4 Focaler-CIoU 火焰烟雾专用训练")
        print("🎯 基于C2f-EMSCP-AAAF架构 + Focaler-CIoU损失")
        print("🧠 改进的边界框回归损失函数")
        print("📊 专门针对火焰烟雾检测任务优化")
        
        # 环境检查
        if not torch.cuda.is_available():
            print("❌ CUDA不可用，请检查GPU环境")
            return None
        
        print(f"✅ GPU: {torch.cuda.get_device_name(0)}")
        print(f"✅ GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
        
        # 加载模型
        config_path = os.path.join(script_dir, 'config.yaml')
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
            
        model = YOLO(config_path)
        
        # 获取专用配置
        aug_params = get_fire_smoke_specialized_augmentation()
        opt_params = get_fire_smoke_optimizer_config()
        loss_params = get_fire_smoke_loss_weights()
        strategy_params = get_fire_smoke_training_strategy()
        focaler_config = get_focaler_ciou_config()
        
        print("\n🔥 火焰烟雾专用增强配置:")
        print("   🎨 颜色保护: 保持火焰橙红色特征")
        print("   🌫️ 烟雾模拟: 增强灰度和密度变化")
        print("   🏙️ 场景复杂: 多场景组合训练")
        print("   🧠 注意力优化: 保护关键特征区域")
        print("   ⚡ 高效训练: 混合精度+缓存加速")
        print(f"\n🎯 Focaler-CIoU损失配置:")
        print(f"   📊 下阈值 d: {focaler_config['d']}")
        print(f"   📊 上阈值 u: {focaler_config['u']}")
        print(f"   📦 边界框权重: {loss_params['box']}")

        # 应用Focaler-CIoU损失函数
        def apply_focaler_ciou_loss(model):
            """将Focaler-CIoU损失函数应用到模型"""
            try:
                # 创建自定义损失函数
                custom_bbox_loss = FocalerCIoUBboxLoss(
                    reg_max=focaler_config['reg_max'],
                    d=focaler_config['d'],
                    u=focaler_config['u']
                )

                # 多种方式尝试替换损失函数
                success = False

                # 方法1: 直接替换检测头的损失函数
                if hasattr(model.model, 'model') and len(model.model.model) > 0:
                    detect_layer = model.model.model[-1]  # 通常是最后一层
                    if hasattr(detect_layer, 'bbox_loss'):
                        detect_layer.bbox_loss = custom_bbox_loss
                        print("✅ 方法1成功: 替换检测头bbox_loss")
                        success = True
                    elif hasattr(detect_layer, 'loss'):
                        # 如果有loss属性，尝试替换其中的bbox部分
                        if hasattr(detect_layer.loss, 'bbox_loss'):
                            detect_layer.loss.bbox_loss = custom_bbox_loss
                            print("✅ 方法1.1成功: 替换loss.bbox_loss")
                            success = True

                # 方法2: 尝试替换模型的损失计算函数
                if not success and hasattr(model.model, 'bbox_loss'):
                    model.model.bbox_loss = custom_bbox_loss
                    print("✅ 方法2成功: 替换模型bbox_loss")
                    success = True

                # 方法3: 通过trainer设置
                if not success:
                    # 将自定义损失函数保存到模型属性中，供trainer使用
                    model.custom_bbox_loss = custom_bbox_loss
                    print("✅ 方法3成功: 设置custom_bbox_loss属性")
                    success = True

                if success:
                    print(f"🎯 Focaler-CIoU配置: d={focaler_config['d']}, u={focaler_config['u']}")
                    return True
                else:
                    print("⚠️ 无法找到合适的损失函数替换点，将使用默认损失函数")
                    return False

            except Exception as e:
                print(f"❌ 应用Focaler-CIoU损失函数失败: {e}")
                import traceback
                traceback.print_exc()
                return False

        # 应用自定义损失函数
        focaler_applied = apply_focaler_ciou_loss(model)

        if not focaler_applied:
            print("⚠️ 继续使用标准CIoU损失函数进行训练")
        
        # 开始专用训练
        results = model.train(
            # === 数据集配置 ===
            data='../../ultralytics/cfg/datasets/fire-smoke-dataset.yaml',
            epochs=100,          # 🕐 完整训练周期（基于0.78mAP成功配置v3）
            batch=24,            # 📦 自动批次大小优化
            imgsz=640,           # 📐 标准分辨率
            
            # === 设备配置 ===
            device=0,            # 🖥️ GPU 0
            workers=4,           # 👥 多进程数据加载
            
            # === 项目配置 ===
            project='runs/train',
            name='innovation4-focaler-ciou-best-config-v3',
            exist_ok=True,
            
            # === 优化器配置 ===
            optimizer="AdamW",
            **opt_params,
            
            # === 火焰烟雾专用数据增强 ===
            **aug_params,
            
            # === 专用损失权重 ===
            **loss_params,
            
            # === 训练策略 ===
            **strategy_params,
            
            # === 其他设置 ===
            verbose=True,
            pretrained=True,     # 🎯 使用预训练权重
        )
        
        print("\n🎉 火焰烟雾专用训练完成！")
        print("📁 结果保存在: runs/train/innovation1-c2f-emscp-aaaf-fire-smoke-specialized")
        print("🔥 专用增强策略充分发挥了C2f-EMSCP-AAAF的优势")
        
        return results
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
