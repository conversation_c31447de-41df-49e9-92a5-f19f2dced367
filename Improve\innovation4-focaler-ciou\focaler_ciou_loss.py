"""
Focaler-CIoU Loss Implementation
===============================

This module implements the Focaler-CIoU loss function as described in the research paper.
The loss combines Complete IoU (CIoU) with a focaler transformation to improve 
bounding box regression performance.

Mathematical Formulation:
- IoU = |B ∩ B^gt| / |B ∪ B^gt|
- CIoU = IoU - ρ²(b,b^gt)/c² - αv
- IoU^focaler = piecewise function with thresholds d=0.00, u=0.95
- L_IoU_Focaler = 1 - CIoU + IoU - IoU^focaler

Author: Innovation Project 4
Date: 2025-01-09
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math


def compute_iou(box1, box2, xywh=False, eps=1e-7):
    """
    Calculate Intersection over Union (IoU) between two sets of boxes.
    
    Args:
        box1 (torch.Tensor): First set of boxes
        box2 (torch.Tensor): Second set of boxes  
        xywh (bool): Whether boxes are in xywh format
        eps (float): Small epsilon to avoid division by zero
        
    Returns:
        torch.Tensor: IoU values
    """
    # Convert to xyxy format if needed
    if xywh:
        (x1, y1, w1, h1), (x2, y2, w2, h2) = box1.chunk(4, -1), box2.chunk(4, -1)
        w1_, h1_, w2_, h2_ = w1 / 2, h1 / 2, w2 / 2, h2 / 2
        b1_x1, b1_x2, b1_y1, b1_y2 = x1 - w1_, x1 + w1_, y1 - h1_, y1 + h1_
        b2_x1, b2_x2, b2_y1, b2_y2 = x2 - w2_, x2 + w2_, y2 - h2_, y2 + h2_
    else:
        b1_x1, b1_y1, b1_x2, b1_y2 = box1.chunk(4, -1)
        b2_x1, b2_y1, b2_x2, b2_y2 = box2.chunk(4, -1)

    # Intersection area
    inter = (torch.min(b1_x2, b2_x2) - torch.max(b1_x1, b2_x1)).clamp(0) * \
            (torch.min(b1_y2, b2_y2) - torch.max(b1_y1, b2_y1)).clamp(0)

    # Union area
    w1, h1 = b1_x2 - b1_x1, b1_y2 - b1_y1 + eps
    w2, h2 = b2_x2 - b2_x1, b2_y2 - b2_y1 + eps
    union = w1 * h1 + w2 * h2 - inter + eps

    # IoU
    iou = inter / union
    return iou.squeeze(-1)


def compute_ciou(box1, box2, xywh=False, eps=1e-7):
    """
    Calculate Complete IoU (CIoU) between two sets of boxes.
    
    Args:
        box1 (torch.Tensor): First set of boxes
        box2 (torch.Tensor): Second set of boxes
        xywh (bool): Whether boxes are in xywh format
        eps (float): Small epsilon to avoid division by zero
        
    Returns:
        torch.Tensor: CIoU values
    """
    # Convert to xyxy format if needed
    if xywh:
        (x1, y1, w1, h1), (x2, y2, w2, h2) = box1.chunk(4, -1), box2.chunk(4, -1)
        w1_, h1_, w2_, h2_ = w1 / 2, h1 / 2, w2 / 2, h2 / 2
        b1_x1, b1_x2, b1_y1, b1_y2 = x1 - w1_, x1 + w1_, y1 - h1_, y1 + h1_
        b2_x1, b2_x2, b2_y1, b2_y2 = x2 - w2_, x2 + w2_, y2 - h2_, y2 + h2_
    else:
        b1_x1, b1_y1, b1_x2, b1_y2 = box1.chunk(4, -1)
        b2_x1, b2_y1, b2_x2, b2_y2 = box2.chunk(4, -1)
        w1, h1 = b1_x2 - b1_x1, b1_y2 - b1_y1 + eps
        w2, h2 = b2_x2 - b2_x1, b2_y2 - b2_y1 + eps

    # Intersection area
    inter = (torch.min(b1_x2, b2_x2) - torch.max(b1_x1, b2_x1)).clamp(0) * \
            (torch.min(b1_y2, b2_y2) - torch.max(b1_y1, b2_y1)).clamp(0)

    # Union area
    union = w1 * h1 + w2 * h2 - inter + eps

    # IoU
    iou = inter / union

    # Center points
    cw = torch.max(b1_x2, b2_x2) - torch.min(b1_x1, b2_x1)  # convex width
    ch = torch.max(b1_y2, b2_y2) - torch.min(b1_y1, b2_y1)  # convex height
    c2 = cw ** 2 + ch ** 2 + eps  # convex diagonal squared
    
    # Center distance squared
    rho2 = ((b1_x1 + b1_x2 - b2_x1 - b2_x2) ** 2 + (b1_y1 + b1_y2 - b2_y1 - b2_y2) ** 2) / 4

    # Aspect ratio consistency term
    v = (4 / math.pi ** 2) * torch.pow(torch.atan(w2 / h2) - torch.atan(w1 / h1), 2)
    alpha = v / (v - iou + (1 + eps))

    # CIoU
    ciou = iou - (rho2 / c2 + alpha * v)
    return ciou.squeeze(-1)


def compute_focaler_iou(iou, d=0.00, u=0.95):
    """
    Apply Focaler transformation to IoU values.
    
    Args:
        iou (torch.Tensor): Original IoU values
        d (float): Lower threshold (default: 0.00)
        u (float): Upper threshold (default: 0.95)
        
    Returns:
        torch.Tensor: Focaler-transformed IoU values
    """
    # Piecewise function implementation
    focaler_iou = torch.zeros_like(iou)
    
    # Case 1: IoU < d -> 0
    mask1 = iou < d
    focaler_iou[mask1] = 0.0
    
    # Case 2: d <= IoU <= u -> (IoU - d) / (u - d)
    mask2 = (iou >= d) & (iou <= u)
    focaler_iou[mask2] = (iou[mask2] - d) / (u - d)
    
    # Case 3: IoU > u -> 1
    mask3 = iou > u
    focaler_iou[mask3] = 1.0
    
    return focaler_iou


def compute_focaler_ciou_loss(pred_boxes, target_boxes, xywh=False, d=0.00, u=0.95, eps=1e-7):
    """
    Compute Focaler-CIoU loss between predicted and target boxes.
    
    Args:
        pred_boxes (torch.Tensor): Predicted bounding boxes
        target_boxes (torch.Tensor): Ground truth bounding boxes
        xywh (bool): Whether boxes are in xywh format
        d (float): Lower threshold for Focaler transformation
        u (float): Upper threshold for Focaler transformation
        eps (float): Small epsilon to avoid division by zero
        
    Returns:
        torch.Tensor: Focaler-CIoU loss values
    """
    # Compute IoU
    iou = compute_iou(pred_boxes, target_boxes, xywh=xywh, eps=eps)
    
    # Compute CIoU
    ciou = compute_ciou(pred_boxes, target_boxes, xywh=xywh, eps=eps)
    
    # Compute Focaler-IoU
    focaler_iou = compute_focaler_iou(iou, d=d, u=u)
    
    # Final Focaler-CIoU loss: L_IoU_Focaler = 1 - CIoU + IoU - IoU^focaler
    loss = 1.0 - ciou + iou - focaler_iou
    
    return loss


class FocalerCIoUBboxLoss(nn.Module):
    """
    Custom Bbox Loss class using Focaler-CIoU instead of regular CIoU.
    
    This class replaces the standard CIoU loss with the Focaler-CIoU implementation
    while maintaining compatibility with the existing training pipeline.
    """
    
    def __init__(self, reg_max=16, d=0.00, u=0.95):
        """
        Initialize the FocalerCIoUBboxLoss module.
        
        Args:
            reg_max (int): Maximum regression value for DFL
            d (float): Lower threshold for Focaler transformation
            u (float): Upper threshold for Focaler transformation
        """
        super().__init__()
        self.reg_max = reg_max
        self.d = d
        self.u = u
        
        # Import DFL loss from ultralytics
        try:
            from ultralytics.utils.loss import DFLoss
            self.dfl_loss = DFLoss(reg_max) if reg_max > 1 else None
        except ImportError:
            print("Warning: Could not import DFLoss from ultralytics. DFL loss will be disabled.")
            self.dfl_loss = None
    
    def forward(self, pred_dist, pred_bboxes, anchor_points, target_bboxes, target_scores, target_scores_sum, fg_mask, mpdiou_hw=None):
        """
        Forward pass for Focaler-CIoU loss computation.
        
        Args:
            pred_dist: Predicted distributions
            pred_bboxes: Predicted bounding boxes
            anchor_points: Anchor points
            target_bboxes: Ground truth bounding boxes
            target_scores: Target scores
            target_scores_sum: Sum of target scores
            fg_mask: Foreground mask
            mpdiou_hw: MPDIoU height-width (unused in this implementation)
            
        Returns:
            tuple: (loss_iou, loss_dfl)
        """
        # Compute weights
        weight = target_scores.sum(-1)[fg_mask].unsqueeze(-1)
        
        # Compute Focaler-CIoU loss
        focaler_ciou_loss = compute_focaler_ciou_loss(
            pred_bboxes[fg_mask], 
            target_bboxes[fg_mask], 
            xywh=False, 
            d=self.d, 
            u=self.u
        )
        
        # Apply weights and normalize
        loss_iou = (focaler_ciou_loss * weight).sum() / target_scores_sum
        
        # DFL loss (unchanged from original implementation)
        if self.dfl_loss:
            try:
                from ultralytics.utils.tal import bbox2dist
                target_ltrb = bbox2dist(anchor_points, target_bboxes, self.dfl_loss.reg_max - 1)
                loss_dfl = self.dfl_loss(pred_dist[fg_mask].view(-1, self.dfl_loss.reg_max), target_ltrb[fg_mask]) * weight
                loss_dfl = loss_dfl.sum() / target_scores_sum
            except ImportError:
                loss_dfl = torch.tensor(0.0).to(pred_dist.device)
        else:
            loss_dfl = torch.tensor(0.0).to(pred_dist.device)
        
        return loss_iou, loss_dfl
