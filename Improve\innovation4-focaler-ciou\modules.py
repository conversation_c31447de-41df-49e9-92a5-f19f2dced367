"""
创新点1: C2f-EMSCP + 自适应注意力融合 (AAAF)
核心模块实现
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from ultralytics.nn.modules.conv import Conv, CBAM
from ultralytics.nn.modules.block import C2f, Bottleneck
from ultralytics.nn.extra_modules.attention import EMA, CAA, MLCA

__all__ = ['AdaptiveAttentionFusion', 'Bottleneck_EMSCP_AAF', 'C2f_EMSCP_AAF']


class EMSConvP(nn.Module):
    """Enhanced Multi-Scale Conv Plus - 复制自原始实现"""
    def __init__(self, channel=256, kernels=[1, 3, 5, 7]):
        super().__init__()
        self.groups = len(kernels)
        min_ch = channel // self.groups
        assert min_ch >= 16, f'channel must Greater than {16 * self.groups}, but {channel}'
        
        self.convs = nn.ModuleList([])
        for ks in kernels:
            self.convs.append(Conv(c1=min_ch, c2=min_ch, k=ks))
        self.conv_1x1 = Conv(channel, channel, k=1)
        
    def forward(self, x):
        # 重新排列输入张量以分组处理
        bs, ch, h, w = x.shape
        x_group = x.view(bs, self.groups, ch // self.groups, h, w)
        
        # 对每个组应用对应的卷积
        x_convs = []
        for i in range(self.groups):
            x_convs.append(self.convs[i](x_group[:, i]))
        
        # 合并所有组的输出
        x_convs = torch.cat(x_convs, dim=1)
        x_convs = self.conv_1x1(x_convs)
        
        return x_convs


class AdaptiveAttentionFusion(nn.Module):
    """自适应注意力融合模块 - 简化版本只使用CBAM"""
    def __init__(self, channels, reduction=16):
        super().__init__()
        self.channels = channels

        # 只使用CBAM注意力机制（最稳定）
        if channels > 0:
            self.cbam = CBAM(channels)
            self.use_attention = True
        else:
            self.use_attention = False
        
    def forward(self, x):
        if self.use_attention:
            # 应用CBAM注意力
            return self.cbam(x)
        else:
            # 如果通道数不够，直接返回输入
            return x


class Bottleneck_EMSCP_AAF(nn.Module):
    """增强的EMSCP Bottleneck with 自适应注意力融合"""
    def __init__(self, c1, c2, shortcut=True, g=1, k=(3, 3), e=0.5):
        super().__init__()
        # 确保所有参数都是有效的整数
        c1, c2 = int(c1), int(c2)
        c_ = int(c2 * e)  # hidden channels

        # 重新定义卷积层
        self.cv1 = Conv(c1, c_, k[0], 1)

        # 检查EMSConvP的通道数要求
        if c2 >= 64:  # EMSConvP需要至少64个通道
            self.cv2 = EMSConvP(c2)
            # 预先创建通道调整层（如果需要）
            if c_ != c2:
                self.channel_adjust = Conv(c_, c2, 1, 1)
            else:
                self.channel_adjust = None
        else:
            # 如果通道数不够，使用标准卷积
            self.cv2 = Conv(c_, c2, k[1], 1, g=g)
            self.channel_adjust = None

        # 添加自适应注意力融合
        self.aaf = AdaptiveAttentionFusion(c2)

        # 残差连接条件
        self.add = shortcut and c1 == c2
        
    def forward(self, x):
        """Forward pass through the bottleneck with AAF."""
        x1 = self.cv1(x)
        # 如果需要通道调整（仅对EMSConvP）
        if hasattr(self, 'channel_adjust') and self.channel_adjust is not None:
            x1 = self.channel_adjust(x1)
        y = self.cv2(x1)

        # 应用自适应注意力融合
        y = self.aaf(y)

        # 残差连接
        return x + y if self.add else y


class C2f_EMSCP_AAF(nn.Module):
    """C2f with EMSCP and Adaptive Attention Fusion"""
    def __init__(self, c1, c2, n=1, shortcut=False, g=1, e=0.5):
        super().__init__()
        # 确保所有参数都是有效的整数
        c1, c2, n = int(c1), int(c2), int(n)
        self.c = int(c2 * e)  # hidden channels
        self.cv1 = Conv(c1, 2 * self.c, 1, 1)
        self.cv2 = Conv((2 + n) * self.c, c2, 1)  # optional act=FReLU(c2)

        # 使用增强的Bottleneck
        self.m = nn.ModuleList(
            Bottleneck_EMSCP_AAF(self.c, self.c, shortcut, g, k=(3, 3), e=1.0)
            for _ in range(n)
        )

        # 在最后添加全局自适应注意力融合
        self.global_aaf = AdaptiveAttentionFusion(c2)
        
    def forward(self, x):
        """Forward pass through C2f layer with AAF."""
        # 标准C2f流程
        y = list(self.cv1(x).chunk(2, 1))
        y.extend(m(y[-1]) for m in self.m)
        output = self.cv2(torch.cat(y, 1))

        # 应用全局自适应注意力融合
        output = self.global_aaf(output)

        return output


def test_modules():
    """测试模块功能"""
    print("🧪 测试创新点1模块...")
    
    # 测试参数
    batch_size = 2
    channels = 256
    height, width = 32, 32
    
    # 创建测试输入
    x = torch.randn(batch_size, channels, height, width)
    print(f"输入张量形状: {x.shape}")
    
    # 测试AdaptiveAttentionFusion
    print("\n1. 测试AdaptiveAttentionFusion...")
    aaf = AdaptiveAttentionFusion(channels)
    out_aaf = aaf(x)
    print(f"AAF输出形状: {out_aaf.shape}")
    print(f"AAF参数量: {sum(p.numel() for p in aaf.parameters()):,}")
    
    # 测试Bottleneck_EMSCP_AAF
    print("\n2. 测试Bottleneck_EMSCP_AAF...")
    bottleneck = Bottleneck_EMSCP_AAF(channels, channels)
    out_bottleneck = bottleneck(x)
    print(f"Bottleneck输出形状: {out_bottleneck.shape}")
    print(f"Bottleneck参数量: {sum(p.numel() for p in bottleneck.parameters()):,}")
    
    # 测试C2f_EMSCP_AAF
    print("\n3. 测试C2f_EMSCP_AAF...")
    c2f = C2f_EMSCP_AAF(channels, channels, n=3)
    out_c2f = c2f(x)
    print(f"C2f输出形状: {out_c2f.shape}")
    print(f"C2f参数量: {sum(p.numel() for p in c2f.parameters()):,}")
    
    print("\n✅ 所有模块测试通过！")


if __name__ == "__main__":
    test_modules()
