#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 火焰烟雾检测手动优化训练
基于参数调优参考文档.md中的最佳配置

应用C2f-EMSCP最优配置和BiFPN成功配置的综合优化方案
目标：快速达到0.77+ mAP50，冲刺0.78+ mAP50
"""

import os
import sys
import warnings
import torch
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from ultralytics import YOLO
from ultralytics.utils import LOGGER

# 忽略警告
warnings.filterwarnings('ignore')

def setup_training_environment():
    """设置训练环境"""
    print("🔧 设置训练环境...")
    
    # 检查CUDA
    if torch.cuda.is_available():
        device_name = torch.cuda.get_device_name(0)
        memory_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"✅ GPU: {device_name}")
        print(f"📊 显存: {memory_gb:.1f}GB")
        
        # 显存优化建议
        if memory_gb < 6:
            print("⚠️ 显存较小，建议降低batch size")
        elif memory_gb >= 8:
            print("🚀 显存充足，可以使用较大batch size")
    else:
        print("⚠️ CUDA不可用，将使用CPU训练")
    
    # 检查数据集
    dataset_path = "ultralytics/cfg/datasets/fire-smoke-dataset.yaml"
    if os.path.exists(dataset_path):
        print(f"✅ 数据集配置: {dataset_path}")
    else:
        raise FileNotFoundError(f"❌ 数据集配置不存在: {dataset_path}")
    
    # 检查预训练权重
    weights_path = "yolov8n.pt"
    if os.path.exists(weights_path):
        print(f"✅ 预训练权重: {weights_path}")
    else:
        print(f"⚠️ 预训练权重不存在，将自动下载: {weights_path}")

def train_with_c2f_emscp_config(model_path="yolov8n.pt"):
    """
    使用C2f-EMSCP最优配置进行训练
    基于文档中的案例1: C2f-EMSCP最优配置
    """
    print("🏆 使用C2f-EMSCP最优配置训练...")
    print("📋 配置来源: 参数调优参考文档.md - 案例1")
    
    try:
        # 加载模型
        model = YOLO(model_path)
        print(f"✅ 模型加载成功: {model_path}")
        
        # 开始训练
        print("🚀 开始训练...")
        results = model.train(
            # === 数据集配置 ===
            data='ultralytics/cfg/datasets/fire-smoke-dataset.yaml',
            
            # === 基本训练参数 ===
            epochs=100,                 # 充分训练
            patience=50,                # 增加耐心
            batch=-1,                   # 自动选择batch size
            imgsz=640,
            
            # === 设备设置 ===
            device="0" if torch.cuda.is_available() else "cpu",
            workers=8,
            
            # === 项目设置 ===
            project='runs/train',
            name='fire-smoke-c2f-emscp-optimized',
            exist_ok=True,
            
            # === 优化器设置 (C2f-EMSCP最优配置) ===
            optimizer='AdamW',
            lr0=0.0008,                 # 保守学习率
            lrf=0.0001,                 # 低最终学习率
            momentum=0.937,
            weight_decay=0.0008,        # 适度正则化
            
            # === 学习率调度 ===
            cos_lr=True,
            warmup_epochs=3.0,          # 充分预热
            warmup_momentum=0.8,
            warmup_bias_lr=0.008,
            
            # === 损失权重 (关键优化) ===
            box=7.5,                    # 标准边界框权重
            cls=0.4,                    # 降低分类权重 - 关键改进点
            dfl=1.5,                    # 标准分布焦点权重
            
            # === 火焰烟雾专用数据增强 ===
            hsv_h=0.010,                # 严格保护火焰色彩
            hsv_s=0.6,
            hsv_v=0.3,
            degrees=8.0,
            translate=0.08,
            scale=0.5,
            shear=0.0,                  # 禁用剪切
            perspective=0.0,            # 禁用透视
            flipud=0.0,                 # 禁用垂直翻转
            fliplr=0.4,
            mosaic=0.5,
            mixup=0.0,                  # 禁用颜色污染
            copy_paste=0.0,             # 禁用复制粘贴
            close_mosaic=15,            # 延长马赛克关闭
            
            # === 其他优化 ===
            amp=True,                   # 混合精度
            cache=True,                 # 缓存加速
            multi_scale=True,           # 多尺度训练
            verbose=True,
            seed=42,
            save_period=10,             # 定期保存
        )
        
        print("✅ C2f-EMSCP配置训练完成!")
        return results
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def train_with_bifpn_config(model_path="yolov8n.pt"):
    """
    使用BiFPN成功配置进行训练
    基于文档中的案例2: BiFPN成功配置
    """
    print("🥈 使用BiFPN成功配置训练...")
    print("📋 配置来源: 参数调优参考文档.md - 案例2")
    
    try:
        # 加载模型
        model = YOLO(model_path)
        print(f"✅ 模型加载成功: {model_path}")
        
        # 开始训练
        print("🚀 开始训练...")
        results = model.train(
            # === 数据集配置 ===
            data='ultralytics/cfg/datasets/fire-smoke-dataset.yaml',
            
            # === 基本训练参数 ===
            epochs=100,                 # 充分训练
            patience=50,
            batch=-1,
            imgsz=640,
            
            # === 设备设置 ===
            device="0" if torch.cuda.is_available() else "cpu",
            workers=8,
            
            # === 项目设置 ===
            project='runs/train',
            name='fire-smoke-bifpn-optimized',
            exist_ok=True,
            
            # === 优化器设置 (BiFPN成功配置) ===
            optimizer='AdamW',
            lr0=0.001,                  # 稍高学习率
            lrf=0.0001,
            momentum=0.937,
            weight_decay=0.0008,
            
            # === 学习率调度 ===
            cos_lr=True,
            warmup_epochs=3.0,
            warmup_momentum=0.8,
            warmup_bias_lr=0.01,
            
            # === 损失权重优化 ===
            box=7.5,
            cls=0.5,                    # 适度降低
            dfl=1.5,
            
            # === 数据增强 (适度保守) ===
            hsv_h=0.015,
            hsv_s=0.7,
            hsv_v=0.4,
            degrees=10.0,
            translate=0.1,
            scale=0.6,
            shear=0.0,
            perspective=0.0,
            flipud=0.0,
            fliplr=0.5,
            mosaic=0.6,
            mixup=0.0,
            copy_paste=0.0,
            close_mosaic=15,            # 延长马赛克关闭
            
            # === 其他优化 ===
            amp=True,
            cache=True,
            multi_scale=True,
            verbose=True,
            seed=42,
            save_period=10,
        )
        
        print("✅ BiFPN配置训练完成!")
        return results
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_training_results(results, config_name):
    """分析训练结果"""
    if results is None:
        print(f"❌ {config_name} 训练失败，无结果可分析")
        return
    
    print(f"\n📊 {config_name} 训练结果分析:")
    print("=" * 50)
    
    try:
        # 获取最终指标
        if hasattr(results, 'results_dict'):
            metrics = results.results_dict
            
            # 提取关键指标
            mAP50 = metrics.get('metrics/mAP50(B)', 0)
            mAP50_95 = metrics.get('metrics/mAP50-95(B)', 0)
            precision = metrics.get('metrics/precision(B)', 0)
            recall = metrics.get('metrics/recall(B)', 0)
            
            print(f"📈 mAP50: {mAP50:.5f}")
            print(f"📈 mAP50-95: {mAP50_95:.5f}")
            print(f"📈 Precision: {precision:.5f}")
            print(f"📈 Recall: {recall:.5f}")
            
            # 与基准对比
            baseline_mAP50 = 0.75445    # 基准模型
            bifpn_mAP50 = 0.76862       # BiFPN
            c2f_emscp_mAP50 = 0.78026   # C2f-EMSCP最佳
            
            if mAP50 > 0:
                improvement_vs_baseline = (mAP50 - baseline_mAP50) * 100
                improvement_vs_bifpn = (mAP50 - bifpn_mAP50) * 100
                
                print(f"\n🎯 性能对比:")
                print(f"  vs 基准模型: {improvement_vs_baseline:+.2f}%")
                print(f"  vs BiFPN: {improvement_vs_bifpn:+.2f}%")
                
                if mAP50 > bifpn_mAP50:
                    print("🎉 成功超越BiFPN!")
                if mAP50 > c2f_emscp_mAP50:
                    print("🚀 创造新的最佳记录!")
                    
        else:
            print("⚠️ 无法获取详细指标")
            
    except Exception as e:
        print(f"⚠️ 结果分析出错: {e}")

def main():
    """主函数"""
    print("🎯 火焰烟雾检测手动优化训练")
    print("基于参数调优参考文档.md的最佳配置")
    print("=" * 60)
    
    try:
        # 1. 环境设置
        setup_training_environment()
        print()
        
        # 2. 选择训练配置
        print("📋 可选训练配置:")
        print("  1. C2f-EMSCP最优配置 (目标: 0.78+ mAP50)")
        print("  2. BiFPN成功配置 (目标: 0.77+ mAP50)")
        print("  3. 两种配置都运行")
        
        choice = input("\n请选择配置 (1/2/3): ").strip()
        
        if choice == "1":
            print("\n🏆 运行C2f-EMSCP最优配置...")
            results = train_with_c2f_emscp_config()
            analyze_training_results(results, "C2f-EMSCP最优配置")
            
        elif choice == "2":
            print("\n🥈 运行BiFPN成功配置...")
            results = train_with_bifpn_config()
            analyze_training_results(results, "BiFPN成功配置")
            
        elif choice == "3":
            print("\n🏆 运行C2f-EMSCP最优配置...")
            results1 = train_with_c2f_emscp_config()
            analyze_training_results(results1, "C2f-EMSCP最优配置")
            
            print("\n🥈 运行BiFPN成功配置...")
            results2 = train_with_bifpn_config()
            analyze_training_results(results2, "BiFPN成功配置")
            
        else:
            print("❌ 无效选择，默认运行C2f-EMSCP配置...")
            results = train_with_c2f_emscp_config()
            analyze_training_results(results, "C2f-EMSCP最优配置")
        
        print("\n" + "=" * 60)
        print("🎉 训练完成!")
        print("📁 结果保存在 runs/train/ 目录下")
        print("📊 建议查看训练曲线和验证结果")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断训练")
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
