#!/usr/bin/env python3
"""
批量运行所有YOLO基准模型训练脚本

此脚本将依次运行YOLOv5n、YOLOv10n和YOLOv11n的基准训练，
用于生成对比实验的基准结果。

使用方法:
    python run_all_baselines.py

注意事项:
    - 确保有足够的GPU内存和存储空间
    - 每个模型训练大约需要几小时时间
    - 建议在训练开始前检查数据集路径是否正确
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def run_training_script(script_path, model_name):
    """
    运行训练脚本
    
    Args:
        script_path (str): 训练脚本的路径
        model_name (str): 模型名称，用于日志输出
    
    Returns:
        bool: 训练是否成功完成
    """
    print(f"\n{'='*60}")
    print(f"开始训练 {model_name}")
    print(f"脚本路径: {script_path}")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*60}")
    
    try:
        # 检查脚本文件是否存在
        if not os.path.exists(script_path):
            print(f"错误: 训练脚本不存在 - {script_path}")
            return False
        
        # 运行训练脚本
        start_time = time.time()
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=False, 
                              text=True, 
                              cwd=os.path.dirname(script_path))
        end_time = time.time()
        
        # 计算训练时间
        training_time = end_time - start_time
        hours = int(training_time // 3600)
        minutes = int((training_time % 3600) // 60)
        seconds = int(training_time % 60)
        
        if result.returncode == 0:
            print(f"\n✅ {model_name} 训练成功完成!")
            print(f"训练耗时: {hours:02d}:{minutes:02d}:{seconds:02d}")
            return True
        else:
            print(f"\n❌ {model_name} 训练失败!")
            print(f"返回码: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"\n❌ {model_name} 训练过程中发生异常: {str(e)}")
        return False

def main():
    """主函数"""
    print("YOLO基准模型批量训练脚本")
    print("=" * 60)
    
    # 获取当前脚本所在目录
    base_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 定义训练脚本路径和模型名称
    training_configs = [
        {
            'script': os.path.join(base_dir, 'yolov5', 'train_yolov5n.py'),
            'name': 'YOLOv5n',
            'description': 'YOLOv5系列最小版本基准模型'
        },
        {
            'script': os.path.join(base_dir, 'yolov10', 'train_yolov10n.py'),
            'name': 'YOLOv10n',
            'description': 'YOLOv10系列最小版本基准模型'
        },
        {
            'script': os.path.join(base_dir, 'yolov11', 'train_yolov11n.py'),
            'name': 'YOLOv11n',
            'description': 'YOLOv11系列最小版本基准模型'
        }
    ]
    
    # 显示训练计划
    print(f"计划训练 {len(training_configs)} 个基准模型:")
    for i, config in enumerate(training_configs, 1):
        print(f"{i}. {config['name']} - {config['description']}")
    
    print(f"\n开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 确认是否继续
    try:
        response = input("\n是否继续执行批量训练? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("训练已取消。")
            return
    except KeyboardInterrupt:
        print("\n训练已取消。")
        return
    
    # 记录训练结果
    results = []
    total_start_time = time.time()
    
    # 依次运行每个训练脚本
    for i, config in enumerate(training_configs, 1):
        print(f"\n进度: {i}/{len(training_configs)}")
        success = run_training_script(config['script'], config['name'])
        results.append({
            'name': config['name'],
            'success': success
        })
        
        # 如果不是最后一个模型，询问是否继续
        if i < len(training_configs):
            try:
                response = input(f"\n是否继续训练下一个模型 ({training_configs[i]['name']})? (Y/n): ").strip().lower()
                if response in ['n', 'no']:
                    print("批量训练已停止。")
                    break
            except KeyboardInterrupt:
                print("\n批量训练已停止。")
                break
    
    # 计算总训练时间
    total_end_time = time.time()
    total_time = total_end_time - total_start_time
    total_hours = int(total_time // 3600)
    total_minutes = int((total_time % 3600) // 60)
    total_seconds = int(total_time % 60)
    
    # 显示训练结果摘要
    print(f"\n{'='*60}")
    print("训练结果摘要")
    print(f"{'='*60}")
    
    successful_count = 0
    for result in results:
        status = "✅ 成功" if result['success'] else "❌ 失败"
        print(f"{result['name']}: {status}")
        if result['success']:
            successful_count += 1
    
    print(f"\n总计: {successful_count}/{len(results)} 个模型训练成功")
    print(f"总耗时: {total_hours:02d}:{total_minutes:02d}:{total_seconds:02d}")
    print(f"完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 提示查看结果
    if successful_count > 0:
        print(f"\n训练结果保存在 'runs/train/' 目录下:")
        print("- fire-smoke-dataset-yolov5n-baseline/")
        print("- fire-smoke-dataset-yolov10n-baseline/")
        print("- fire-smoke-dataset-yolov11n-baseline/")
        print("\n可以使用这些基准结果与改进模型进行性能对比。")

if __name__ == "__main__":
    main()
