import os
import csv
import math
import time
from datetime import datetime

import numpy as np
import matplotlib.pyplot as plt
from ultralytics.utils.plotting import plot_results

# Configuration for synthetic generation
EPOCHS = 100
SEED = 42
np.random.seed(SEED)

# Target end metrics (approximate)
END_P = 0.82
END_R = 0.74
END_MAP50 = float(np.random.uniform(0.800, 0.805))
END_MAP50_95 = 0.45

# Start metrics (approximate, inspired by existing results)
START_P = 0.55
START_R = 0.43
START_MAP50 = 0.45
START_MAP50_95 = 0.20

# Learning rate schedule (cosine) to mimic Ultralytics style
LR0 = 6e-4
LRF = 1e-4

# Loss ranges (approximate decreasing trends)
START_T_BOX, END_T_BOX = 1.90, 1.26
START_T_CLS, END_T_CLS = 2.25, 0.80
START_T_DFL, END_T_DFL = 1.68, 1.21

START_V_BOX, END_V_BOX = 1.60, 1.20
START_V_CLS, END_V_CLS = 4.00, 1.30
START_V_DFL, END_V_DFL = 1.30, 1.05

# Small jitter helper

def smooth_transition(start, end, n, nonlin=1.2, noise=0.0):
    x = np.linspace(0, 1, n)
    # ease-out curve for nicer convergence
    x = 1 - (1 - x) ** nonlin
    y = start + (end - start) * x
    if noise > 0:
        y += np.random.normal(0, noise, size=y.shape)
    return y


def cosine_lr(epoch_idx, total_epochs):
    # epoch_idx: 0..E-1
    ratio = epoch_idx / max(1, total_epochs - 1)
    return LRF + (LR0 - LRF) * (1 + math.cos(math.pi * ratio)) / 2


def ensure_dir(p):
    os.makedirs(p, exist_ok=True)


def generate_results_csv(out_dir):
    header = [
        "epoch",
        "train/box_loss",
        "train/cls_loss",
        "train/dfl_loss",
        "metrics/precision(B)",
        "metrics/recall(B)",
        "metrics/mAP50(B)",
        "metrics/mAP50-95(B)",
        "val/box_loss",
        "val/cls_loss",
        "val/dfl_loss",
        "lr/pg0",
        "lr/pg1",
        "lr/pg2",
    ]

    t_box = smooth_transition(START_T_BOX, END_T_BOX, EPOCHS, nonlin=1.4, noise=0.01)
    t_cls = smooth_transition(START_T_CLS, END_T_CLS, EPOCHS, nonlin=1.5, noise=0.015)
    t_dfl = smooth_transition(START_T_DFL, END_T_DFL, EPOCHS, nonlin=1.3, noise=0.008)

    v_box = smooth_transition(START_V_BOX, END_V_BOX, EPOCHS, nonlin=1.35, noise=0.012)
    v_cls = smooth_transition(START_V_CLS, END_V_CLS, EPOCHS, nonlin=1.6, noise=0.03)
    v_dfl = smooth_transition(START_V_DFL, END_V_DFL, EPOCHS, nonlin=1.25, noise=0.008)

    prec = smooth_transition(START_P, END_P, EPOCHS, nonlin=1.15, noise=0.004)
    reca = smooth_transition(START_R, END_R, EPOCHS, nonlin=1.18, noise=0.004)
    map50 = smooth_transition(START_MAP50, END_MAP50, EPOCHS, nonlin=1.2, noise=0.0)
    map5095 = smooth_transition(START_MAP50_95, END_MAP50_95, EPOCHS, nonlin=1.25, noise=0.003)

    # clamp ranges sensibly
    def clamp01(arr):
        return np.clip(arr, 0.0, 1.0)

    prec = clamp01(prec)
    reca = clamp01(reca)
    map50 = clamp01(map50)
    map5095 = clamp01(map5095)
    # ensure final mAP@0.5 is in [0.800, 0.805]
    map50[-1] = END_MAP50

    rows = []
    for i in range(EPOCHS):
        epoch_num = i + 1
        lr = cosine_lr(i, EPOCHS)
        row = [
            epoch_num,
            round(float(t_box[i]), 4),
            round(float(t_cls[i]), 4),
            round(float(t_dfl[i]), 4),
            round(float(prec[i]), 5),
            round(float(reca[i]), 5),
            round(float(map50[i]), 5),
            round(float(map5095[i]), 5),
            round(float(v_box[i]), 4),
            round(float(v_cls[i]), 4),
            round(float(v_dfl[i]), 4),
            round(lr, 8),
            round(lr * 0.33, 8),
            round(lr * 0.33, 8),
        ]
        rows.append(row)

    out_csv = os.path.join(out_dir, "results.csv")
    with open(out_csv, "w", newline="", encoding="utf-8") as f:
        writer = csv.writer(f)
        writer.writerow(header)
        writer.writerows(rows)
    return out_csv, {
        "train/box_loss": t_box,
        "train/cls_loss": t_cls,
        "train/dfl_loss": t_dfl,
        "val/box_loss": v_box,
        "val/cls_loss": v_cls,
        "val/dfl_loss": v_dfl,
        "metrics/precision(B)": prec,
        "metrics/recall(B)": reca,
        "metrics/mAP50(B)": map50,
        "metrics/mAP50-95(B)": map5095,
    }


def plot_results_png(out_dir, series):
    epochs = np.arange(1, EPOCHS + 1)
    plt.figure(figsize=(12, 8))
    plt.subplot(2, 2, 1)
    plt.plot(epochs, series["train/box_loss"], label="train/box_loss")
    plt.plot(epochs, series["train/cls_loss"], label="train/cls_loss")
    plt.plot(epochs, series["train/dfl_loss"], label="train/dfl_loss")
    plt.title("Train Losses")
    plt.xlabel("epoch")
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.subplot(2, 2, 2)
    plt.plot(epochs, series["val/box_loss"], label="val/box_loss")
    plt.plot(epochs, series["val/cls_loss"], label="val/cls_loss")
    plt.plot(epochs, series["val/dfl_loss"], label="val/dfl_loss")
    plt.title("Val Losses")
    plt.xlabel("epoch")
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.subplot(2, 2, 3)
    plt.plot(epochs, series["metrics/precision(B)"], label="precision")
    plt.plot(epochs, series["metrics/recall(B)"], label="recall")
    plt.title("P & R vs Epoch")
    plt.xlabel("epoch")
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.subplot(2, 2, 4)
    plt.plot(epochs, series["metrics/mAP50(B)"], label="mAP@0.5")
    plt.plot(epochs, series["metrics/mAP50-95(B)"], label="mAP@0.5:0.95")
    plt.title("mAP vs Epoch")
    plt.xlabel("epoch")
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    out_png = os.path.join(out_dir, "results.png")
    plt.savefig(out_png, dpi=180)
    plt.close()


def synth_precision_recall_curves(n=200):
    # Confidence threshold from 0..1
    t = np.linspace(0, 1, n)
    # Precision typically decreases as threshold lowers; recall increases
    precision = 0.9 - 0.5 * (1 - t**0.6)  # from ~0.9 at high thr to ~0.4 at low thr
    recall = 0.35 + 0.65 * (1 - t**1.3)   # from ~0.35 to ~1.0 as thr lowers
    precision = np.clip(precision, 0, 1)
    recall = np.clip(recall, 0, 1)
    f1 = 2 * precision * recall / np.clip((precision + recall), 1e-9, None)
    # Adjust global level to roughly match END_P/END_R
    # Scale precision/recall so that at a moderate threshold (~0.5) we get close to END_P/END_R
    idx = int(0.5 * (n - 1))
    scale_p = END_P / max(precision[idx], 1e-6)
    scale_r = END_R / max(recall[idx], 1e-6)
    precision = np.clip(precision * scale_p, 0, 1)
    recall = np.clip(recall * scale_r, 0, 1)
    f1 = 2 * precision * recall / np.clip((precision + recall), 1e-9, None)
    return t, precision, recall, f1


def plot_curves(out_dir):
    t, p, r, f1 = synth_precision_recall_curves()

    # P_curve
    plt.figure(figsize=(6, 4))
    plt.plot(t, p, label="Precision")
    plt.xlabel("Confidence threshold")
    plt.ylabel("Precision")
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(os.path.join(out_dir, "P_curve.png"), dpi=180)
    plt.close()

    # R_curve
    plt.figure(figsize=(6, 4))
    plt.plot(t, r, label="Recall", color="tab:orange")
    plt.xlabel("Confidence threshold")
    plt.ylabel("Recall")
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(os.path.join(out_dir, "R_curve.png"), dpi=180)
    plt.close()

    # PR_curve
    plt.figure(figsize=(5, 5))
    plt.plot(r, p, label="PR")
    plt.xlabel("Recall")
    plt.ylabel("Precision")
    plt.xlim(0, 1)
    plt.ylim(0, 1)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(os.path.join(out_dir, "PR_curve.png"), dpi=180)
    plt.close()

    # F1_curve
    plt.figure(figsize=(6, 4))
    plt.plot(t, f1, label="F1", color="tab:green")
    plt.xlabel("Confidence threshold")
    plt.ylabel("F1")
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(os.path.join(out_dir, "F1_curve.png"), dpi=180)
    plt.close()


def main():
    ts = datetime.now().strftime("%Y%m%d_%H%M%S")
    out_dir = os.path.join("mock", f"new_arch_estimate_{ts}")
    ensure_dir(out_dir)

    csv_path, series = generate_results_csv(out_dir)
    # Use Ultralytics plotting to strictly match YOLO results.png style
    plot_results(file=csv_path)
    plot_curves(out_dir)

    # Write a tiny readme
    with open(os.path.join(out_dir, "README.txt"), "w", encoding="utf-8") as f:
        f.write("Synthetic results generated for new architecture estimate.\n")
        f.write(f"Target mAP@0.5 ~ {END_MAP50}\n")
        f.write("Files: results.csv, results.png, P_curve.png, R_curve.png, PR_curve.png, F1_curve.png\n")

    print(out_dir)


if __name__ == "__main__":
    main()

